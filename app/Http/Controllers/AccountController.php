<?php

namespace App\Http\Controllers;

use App\Exports\AccountExport;
use App\Exports\AccountTemplateExport;
use App\Http\Helpers;
use App\Http\Requests\Account\IdsStringRequest;
use App\Http\Requests\Account\StoreRequest;
use App\Http\Requests\Account\UpdateRequest;
use App\Http\Requests\Account\UpdateTimeExpiredRequest;
use App\Imports\AccountImport;
use App\Models\Account;
use App\Models\AccountType;
use App\Models\LoginHistory;
use App\Models\School;
use DateTime;
use Illuminate\Auth\Access\AuthorizationException;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;
use Maatwebsite\Excel\Facades\Excel;

class AccountController extends Controller
{
    /**
     * @throws AuthorizationException
     */
    public function index(Request $request)
    {
        $this->authorize('show-account', Account::class);
        $searchProvince = $request->input('province');
        $searchDistrict = $request->input('district');
        $keyword = $request->input('search');
        $viewMode = $request->input('view_mode', 'account'); // Default to account view

        $provinces = [];
        $districts = [];
        $selectedDistricts = [];
        try{
            $provinces = Helpers::getProvinces();
            $districts = Helpers::getDistricts();
            if($searchProvince != null){
                $selectedDistricts = Helpers::getDistrictsByProvinceId($searchProvince);
            }
        } catch (\Exception $e) {
        }

        // Account view mode
        if ($viewMode === 'account') {
            $accountQuery = Account::query()
                ->whereIn('status', [Account::STATUS_ACTIVE, Account::STATUS_LOCK]);

            if($searchProvince != null && $searchProvince != -1){
                $accountQuery = $accountQuery->where('province', $searchProvince);
            }

            if($searchDistrict != null && $searchDistrict != -1){
                $accountQuery = $accountQuery->where('district', $searchDistrict);
            }

            if($keyword != null){
                $accountQuery = $accountQuery->where(function ($query) use ($keyword) {
                    $query->where('name', 'like', "%$keyword%")
                        ->orWhere('email', 'like', "%$keyword%")
                        ->orWhere('phone_number', 'like', "%$keyword%")
                        ->orWhere(function($subQuery) use ($keyword) {
                            // Check if keyword contains only ASCII characters (normalized search)
                            $normalizedKeyword = $this->normalizeSearchKeyword($keyword);
                            $isNormalizedSearch = ($normalizedKeyword === strtolower($keyword)) && preg_match('/^[a-z0-9\s]+$/i', $keyword);

                            if ($isNormalizedSearch) {
                                // For normalized search (like "son dong"), use the helper function
                                $normalizedQuery = $this->buildNormalizedSearchQuery('name_org');
                                $subQuery->whereRaw($normalizedQuery, ["%".strtolower($keyword)."%"]);
                            } else {
                                // For Vietnamese character search, use binary collation with case-insensitive approach
                                $lowerKeyword = mb_strtolower($keyword, 'UTF-8');
                                $subQuery->whereRaw('LOWER(name_org) COLLATE utf8mb4_bin LIKE ?', ["%".$lowerKeyword."%"]);
                            }
                        });
                });
            }

            $accounts = $accountQuery
                ->sortable()
                ->paginate(100);

            return view('account.index', compact('accounts', 'provinces', 'districts', 'searchDistrict',
                'searchProvince', 'selectedDistricts', 'keyword', 'viewMode'));
        }
        // School view mode
        else {
            // Use the School model directly
            $schoolQuery = School::query()->sortable();

            // Apply filters
            if ($searchProvince != null && $searchProvince != -1) {
                $schoolQuery = $schoolQuery->where('province', $searchProvince);
            }

            if ($searchDistrict != null && $searchDistrict != -1) {
                $schoolQuery = $schoolQuery->where('district', $searchDistrict);
            }

            if ($keyword != null) {
                $schoolQuery = $schoolQuery->where(function($query) use ($keyword) {
                    // Check if keyword contains only ASCII characters (normalized search)
                    $normalizedKeyword = $this->normalizeSearchKeyword($keyword);
                    $isNormalizedSearch = ($normalizedKeyword === strtolower($keyword)) && preg_match('/^[a-z0-9\s]+$/i', $keyword);

                    if ($isNormalizedSearch) {
                        // For normalized search (like "son dong"), use the helper function
                        $normalizedQuery = $this->buildNormalizedSearchQuery('name');
                        $query->whereRaw($normalizedQuery, ["%".strtolower($keyword)."%"]);
                    } else {
                        // For Vietnamese character search, use binary collation with case-insensitive approach
                        $lowerKeyword = mb_strtolower($keyword, 'UTF-8');
                        $query->whereRaw('LOWER(name) COLLATE utf8mb4_bin LIKE ?', ["%".$lowerKeyword."%"]);
                    }
                });
            }

            // Get paginated results
            $schools = $schoolQuery->paginate(100);

            return view('account.index', compact('schools', 'provinces', 'districts', 'searchDistrict',
                'searchProvince', 'selectedDistricts', 'keyword', 'viewMode'));
        }
    }

    /**
     * @throws AuthorizationException
     */
    public function show($id)
    {
        $this->authorize('show-account', Account::class);
        $account = Account::query()
            ->with('school')
            ->whereIn('status', [Account::STATUS_ACTIVE, Account::STATUS_LOCK])
            ->find($id);

        if(!$account){
            $this->flashMessage('warning', 'Account not found!', 'danger');
            return redirect()->route('account');
        }

        $provinces = [];
        $districts = [];
        $accountDistricts = [];

        try{
            $provinces = Helpers::getProvinces();
            $districts = Helpers::getDistricts();

            if(count($provinces) > 0){
                if($account->district != null){
                    $accountDistricts = Helpers::getDistrictsByProvinceId($account->province);
                } else {
                    $accountDistricts = Helpers::getDistrictsByProvinceId(array_keys($provinces)[0]);
                }
            }
        } catch (\Exception $e) {
        }

        $mamnonAccountTypes = AccountType::all();
        $tieuhocAccountTypes = AccountType::all();

        /** @noinspection PhpUndefinedFieldInspection */
        $loginHistories = LoginHistory::query()->where('account_id', $account->id)->orderBy('created_at', 'desc')->paginate(15);

        return view('account.show',compact('account', 'loginHistories', 'districts', 'provinces', 'accountDistricts', 'mamnonAccountTypes', 'tieuhocAccountTypes'));
    }

    /**
     * @throws AuthorizationException
     */
    public function create()
    {
        $this->authorize('create-account', Account::class);
        $provinces = [];
        $firstDistricts = [];

        try{
            $provinces = Helpers::getProvinces();

            if(count($provinces) > 0){
                $firstDistricts = Helpers::getDistrictsByProvinceId(array_keys($provinces)[0]);
            }

        } catch (\Exception $e) {
        }

        $mamnonAccountTypes = AccountType::all();
        $tieuhocAccountTypes = AccountType::all();

        return view('account.create', compact('provinces', 'firstDistricts', 'mamnonAccountTypes', 'tieuhocAccountTypes'));
    }

    /**
     * @throws AuthorizationException
     */
    public function edit($id)
    {
        $this->authorize('edit-account', Account::class);
        $account = Account::query()
            ->find($id);

        if(!$account){
            $this->flashMessage('account', 'Account not found!', 'danger');
            return redirect()->route('account');
        }

        $provinces = [];
        $accountDistricts = [];

        try{
            $provinces = Helpers::getProvinces();

            if(count($provinces) > 0){
                if($account->district != null){
                    $accountDistricts = Helpers::getDistrictsByProvinceId($account->province);
                } else {
                    $accountDistricts = Helpers::getDistrictsByProvinceId(array_keys($provinces)[0]);
                }
            }

        } catch (\Exception $e) {
        }

        $mamnonAccountTypes = AccountType::all();
        $tieuhocAccountTypes = AccountType::all();

        return view('account.edit',compact('account', 'provinces', 'accountDistricts', 'mamnonAccountTypes', 'tieuhocAccountTypes'));
    }

    public function store(StoreRequest $request): RedirectResponse
    {
        $this->authorize('create-account', Account::class);
        
        // Force delete any existing soft-deleted accounts with same phone or email
        Account::withTrashed()
            ->where('phone_number', $request->input('phone_number'))
            ->orWhere('email', $request->input('email'))
            ->forceDelete();

        $token = $this->generateRandomString();

        // init software type
        $softwareTypes = $request->input('software_types');
        $isMamnon = 0;
        $isTieuhoc = 0;
        $mamnonAccountTypeId = null;
        $tieuhocAccountTypeId = null;

        if(in_array(Account::SOFTWARE_MAMNON, $softwareTypes)){
            $isMamnon = 1;
            $mamnonAccountTypeId = $request->input('mamnon_account_type');
        }

        if(in_array(Account::SOFTWARE_TIEUHOC, $softwareTypes)){
            $isTieuhoc = 1;
            $tieuhocAccountTypeId = $request->input('tieuhoc_account_type');
        }

        // Check if a school exists with the same name_org
        $schoolName = $request->input('name_org');
        $school = null;

        if (!empty($schoolName)) {
            $school = School::where('name', $schoolName)->first();

            // If school doesn't exist, create it
            if (!$school) {
                $school = School::create([
                    'name' => $schoolName,
                    'province' => $request->input('province'),
                    'district' => $request->input('district'),
                    'time_expired' => $request->input('time_expired'),
                    'is_mamnon' => $isMamnon,
                    'is_tieuhoc' => $isTieuhoc,
                    'mamnon_account_type_id' => $mamnonAccountTypeId,
                    'tieuhoc_account_type_id' => $tieuhocAccountTypeId,
                ]);
            }
        }

        $account = Account::query()->create([
            'name' => $request->input('name'),
            'name_org' => $request->input('name_org'),
            'email' => $request->input('email'),
            'phone_number' => $request->input('phone_number'),
            'status' => $request->input('status'),
            'class' => $request->input('class'),
            'token' => $token,
            'province' => $request->input('province'),
            'district' => $request->input('district'),
            'is_mamnon' => $isMamnon,
            'is_tieuhoc' => $isTieuhoc,
            'mamnon_account_type_id' => $mamnonAccountTypeId,
            'tieuhoc_account_type_id' => $tieuhocAccountTypeId,
            'password' => Hash::make($request->input('phone_number')),
            'school_id' => $school ? $school->id : null
        ]);

        $this->flashMessage('check', 'Account successfully added!', 'success');
        /** @noinspection PhpUndefinedFieldInspection */
        return redirect()->route('account.show', ['id' => $account->id]);
    }

    public function update(UpdateRequest $request, $id): RedirectResponse
    {
        $this->authorize('edit-account', Account::class);

        // Debug: Log the incoming email value
        \Log::info('Account update attempt', [
            'account_id' => $id,
            'old_email' => Account::find($id)->email ?? 'not found',
            'new_email' => $request->input('email'),
            'all_request_data' => $request->all()
        ]);

        // Additional security check for unauthorized form submissions
        if ($request->has('no_permission') && $request->input('no_permission') === 'true') {
            $this->flashMessage('warning', 'You do not have permission to edit account information', 'danger');
            return redirect()->route('account.show', $id);
        }

        $account = Account::query()
            ->find($id);

        if(!$account){
            $this->flashMessage('warning', 'Account not found!', 'danger');
            return redirect()->route('account.edit', $account->id);
        }

        // Debug: Check for duplicate emails
        $newEmail = $request->input('email');
        $duplicateEmails = Account::withTrashed()
            ->where('email', $newEmail)
            ->where('id', '<>', $id)
            ->get();

        if ($duplicateEmails->count() > 0) {
            \Log::warning('Duplicate emails found during update', [
                'account_id' => $id,
                'email' => $newEmail,
                'duplicates' => $duplicateEmails->map(function($acc) {
                    return [
                        'id' => $acc->id,
                        'name' => $acc->name,
                        'deleted_at' => $acc->deleted_at,
                        'created_at' => $acc->created_at
                    ];
                })->toArray()
            ]);
        }

        // init software type
        $softwareTypes = $request->input('software_types', []);
        $isMamnon = 0;
        $isTieuhoc = 0;
        $mamnonAccountTypeId = null;
        $tieuhocAccountTypeId = null;

        // Log software types for debugging
        \Log::info('Software types from request:', ['software_types' => $softwareTypes]);

        // Check if software types is an array before using in_array
        if (is_array($softwareTypes)) {
            if(in_array(Account::SOFTWARE_MAMNON, $softwareTypes)){
                $isMamnon = 1;
                $mamnonAccountTypeId = $request->input('mamnon_account_type');
            }

            if(in_array(Account::SOFTWARE_TIEUHOC, $softwareTypes)){
                $isTieuhoc = 1;
                $tieuhocAccountTypeId = $request->input('tieuhoc_account_type');
            }
        } else {
            // If software_types is not an array, use the existing account values
            $isMamnon = $account->is_mamnon;
            $isTieuhoc = $account->is_tieuhoc;
            $mamnonAccountTypeId = $account->mamnon_account_type_id;
            $tieuhocAccountTypeId = $account->tieuhoc_account_type_id;

            \Log::warning('Software types is not an array, using existing account values', [
                'is_mamnon' => $isMamnon,
                'is_tieuhoc' => $isTieuhoc
            ]);
        }

        // Check if name_org has changed
        $newNameOrg = $request->input('name_org');
        $school = null;

        if (!empty($newNameOrg) && $newNameOrg !== $account->name_org) {
            // Look for a school with the new name
            $school = School::where('name', $newNameOrg)->first();

            // If school doesn't exist, create it
            if (!$school) {
                $school = School::create([
                    'name' => $newNameOrg,
                    'province' => $request->input('province'),
                    'district' => $request->input('district'),
                    'time_expired' => $request->input('time_expired'),
                    'is_mamnon' => $isMamnon,
                    'is_tieuhoc' => $isTieuhoc,
                    'mamnon_account_type_id' => $mamnonAccountTypeId,
                    'tieuhoc_account_type_id' => $tieuhocAccountTypeId,
                ]);
            }
        } elseif (!empty($newNameOrg) && $account->school_id === null) {
            // If name_org hasn't changed but account doesn't have a school yet
            $school = School::where('name', $newNameOrg)->first();

            // If school doesn't exist, create it
            if (!$school) {
                $school = School::create([
                    'name' => $newNameOrg,
                    'province' => $request->input('province'),
                    'district' => $request->input('district'),
                    'time_expired' => $request->input('time_expired'),
                    'is_mamnon' => $isMamnon,
                    'is_tieuhoc' => $isTieuhoc,
                    'mamnon_account_type_id' => $mamnonAccountTypeId,
                    'tieuhoc_account_type_id' => $tieuhocAccountTypeId,
                ]);
            }
        } elseif ($account->school_id !== null) {
            // If account already has a school, check if we need to update account types
            $school = School::find($account->school_id);

            if ($school) {
                // Check if school's account types have changed since last update
                $schoolTypesChanged = false;

                if ($account->is_mamnon != $school->is_mamnon ||
                    $account->is_tieuhoc != $school->is_tieuhoc ||
                    $account->mamnon_account_type_id != $school->mamnon_account_type_id ||
                    $account->tieuhoc_account_type_id != $school->tieuhoc_account_type_id) {
                    $schoolTypesChanged = true;
                }

                // Always sync with school by default, unless explicitly disabled
                if ((!$request->has('sync_with_school') || $request->input('sync_with_school') != 0) && $schoolTypesChanged) {
                    \Log::info('School account types have changed, updating account', [
                        'school_id' => $school->id,
                        'old_is_mamnon' => $account->is_mamnon,
                        'new_is_mamnon' => $school->is_mamnon,
                        'old_is_tieuhoc' => $account->is_tieuhoc,
                        'new_is_tieuhoc' => $school->is_tieuhoc,
                        'old_mamnon_type' => $account->mamnon_account_type_id,
                        'new_mamnon_type' => $school->mamnon_account_type_id,
                        'old_tieuhoc_type' => $account->tieuhoc_account_type_id,
                        'new_tieuhoc_type' => $school->tieuhoc_account_type_id
                    ]);

                    // Update account types to match school
                    $isMamnon = $school->is_mamnon;
                    $isTieuhoc = $school->is_tieuhoc;
                    $mamnonAccountTypeId = $school->mamnon_account_type_id;
                    $tieuhocAccountTypeId = $school->tieuhoc_account_type_id;
                }
            }
        }

        // Log all request data for debugging
        \Log::info('Account update - All request data: ', $request->all());
        \Log::info('Account update - Status value: ' . $request->input('status'));
        \Log::info('sync_with_school from request: ' . $request->input('sync_with_school'));

        // Force status to be an integer
        $status = (int)$request->input('status');
        \Log::info('Account update - Converted status value: ' . $status);

        $updateData = [
            'name' => $request->input('name'),
            'name_org' => $request->input('name_org'),
            'email' => $request->input('email'),
            'phone_number' => $request->input('phone_number'),
            'status' => $status, // Use the converted integer value
            'province' => $request->input('province'),
            'district' => $request->input('district'),
            'class' => $request->input('class'),
            'is_mamnon' => $isMamnon,
            'is_tieuhoc' => $isTieuhoc,
            'mamnon_account_type_id' => $mamnonAccountTypeId,
            'tieuhoc_account_type_id' => $tieuhocAccountTypeId,
            'sync_with_school' => $request->input('sync_with_school', 0) == '1' ? 1 : 0,
        ];

        \Log::info('sync_with_school in updateData: ' . $updateData['sync_with_school']);

        // Update school_id if a new school was found or created
        if ($school) {
            $updateData['school_id'] = $school->id;

            // Always synchronize account types with school types by default
            // Only skip synchronization if sync_with_school is explicitly set to 0
            if (!$request->has('sync_with_school') || $request->input('sync_with_school') != 0) {
                // Use school's software types and account types
                $updateData['is_mamnon'] = $school->is_mamnon;
                $updateData['is_tieuhoc'] = $school->is_tieuhoc;
                $updateData['mamnon_account_type_id'] = $school->mamnon_account_type_id;
                $updateData['tieuhoc_account_type_id'] = $school->tieuhoc_account_type_id;

                // Also sync province and district
                $updateData['province'] = $school->province;
                $updateData['district'] = $school->district;

                // Sync time_expired as well
                $updateData['time_expired'] = $school->time_expired;

                \Log::info('Synchronized account data with school', [
                    'school_id' => $school->id,
                    'is_mamnon' => $school->is_mamnon,
                    'is_tieuhoc' => $school->is_tieuhoc,
                    'mamnon_account_type_id' => $school->mamnon_account_type_id,
                    'tieuhoc_account_type_id' => $school->tieuhoc_account_type_id,
                    'province' => $school->province,
                    'district' => $school->district,
                    'time_expired' => $school->time_expired
                ]);
            } else {
                \Log::info('Skipped synchronization with school (explicitly disabled)', [
                    'school_id' => $school->id
                ]);
            }
        }

        // Handle time_expired field
        if ($request->has('time_expired') && !empty($request->input('time_expired'))) {
            $timeExpired = DateTime::createFromFormat("Y-m-d\TH:i", $request->input('time_expired'));
            $updateData['time_expired'] = $timeExpired;
        }

        // Explicitly set the status to ensure it's properly saved
        $account->status = $status;

        // Fill other data
        $account->fill($updateData);

        // Handle password logic
        $usePhoneAsPassword = $request->input('phone_number_as_password', 1);
        $account->phone_number_as_password = $usePhoneAsPassword;
        if ($usePhoneAsPassword) {
            // Always use phone number as password
            $account->password = Hash::make($account->phone_number);
            $account->password_manual = null;
        } else {
            // Use manual password if provided
            $manualPassword = $request->input('password_manual');
            if (!empty($manualPassword)) {
                $account->password = Hash::make($manualPassword);
                $account->password_manual = null; // Don't store plain password
            }
        }

        // Log the account data before saving
        \Log::info('Account data before saving:', $account->toArray());

        // Save the account
        $account->save();

        // Log the account data after saving to confirm the status was properly saved
        \Log::info('Account data after saving:', $account->fresh()->toArray());

        $this->flashMessage('check', 'Account updated successfully!', 'success');
        return redirect()->route('account.show', $account->id);
    }

    private function generateRandomString(): string
    {
        $characters = '0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ';
        $charactersLength = strlen($characters);
        $randomString = '';
        for ($i = 0; $i < 10; $i++) {
            $randomString .= $characters[random_int(0, $charactersLength - 1)];
        }
        return $randomString;
    }

    // Commented out removed functionality
    // public function refreshToken($id): RedirectResponse
    // {
    //     $this->authorize('edit-account', Account::class);
    //     $account = Account::query()->find($id);
    //
    //     if($account == null){
    //         $this->flashMessage('warning', 'Account not found!', 'danger');
    //         // @noinspection PhpUndefinedFieldInspection
    //         return redirect()->route('account.show', ['id' => $account->id]);
    //     }
    //
    //     $token = $this->generateRandomString();
    //     $account->update(['token' => $token]);
    //     $account->save();
    //     $this->flashMessage('check', 'Token updated!', 'success');
    //     // @noinspection PhpUndefinedFieldInspection
    //     return redirect()->route('account.show', ['id' => $account->id]);
    // }

    // Commented out removed functionality
    // public function resetComputer($id): RedirectResponse
    // {
    //     $this->authorize('edit-account', Account::class);
    //     $account = Account::query()->find($id);
    //
    //     if($account == null){
    //         $this->flashMessage('warning', 'Account not found!', 'danger');
    //         // @noinspection PhpUndefinedFieldInspection
    //         return redirect()->route('account.show', ['id' => $account->id]);
    //     }
    //
    //     $account->fill([
    //         'ram_computer' => null,
    //         'cpu_computer' => null,
    //         'vendor_computer' => null
    //     ]);
    //     $account->save();
    //
    //     $this->flashMessage('check', 'Reset Computer!', 'success');
    //     // @noinspection PhpUndefinedFieldInspection
    //     return redirect()->route('account.show', ['id' => $account->id]);
    // }

    // Commented out removed functionality
    // public function resetPassword($id): RedirectResponse
    // {
    //     $this->authorize('edit-account', Account::class);
    //     $account = Account::query()->find($id);
    //
    //     if($account == null){
    //         $this->flashMessage('warning', 'Account not found!', 'danger');
    //         // @noinspection PhpUndefinedFieldInspection
    //         return redirect()->route('account.show', ['id' => $account->id]);
    //     }
    //
    //     $password = '123456';
    //     if($account->phone_number != null){
    //         $password = $account->phone_number;
    //     }
    //     $account->fill([
    //         'password' => Hash::make($password)
    //     ]);
    //     $account->save();
    //
    //     $this->flashMessage('check', 'Reset Password!', 'success');
    //     // @noinspection PhpUndefinedFieldInspection
    //     return redirect()->route('account.show', ['id' => $account->id]);
    // }

    /**
     * Toggle the status of an account between active and locked.
     *
     * @param Request $request
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     * @throws AuthorizationException
     */
    public function toggleStatus(Request $request, $id)
    {
        $this->authorize('edit-account', Account::class);
        $account = Account::query()->find($id);

        if(!$account){
            return response()->json(['success' => false, 'message' => __('messages.alert.not_found')]);
        }

        // Toggle the status
        $newStatus = $account->status == Account::STATUS_ACTIVE ? Account::STATUS_LOCK : Account::STATUS_ACTIVE;

        $account->fill([
            'status' => $newStatus
        ]);
        $account->save();

        $statusText = $newStatus == Account::STATUS_ACTIVE ? __('messages.status.active') : __('messages.status.locked');
        $statusClass = $newStatus == Account::STATUS_ACTIVE ? 'success' : 'danger';
        $statusIcon = $newStatus == Account::STATUS_ACTIVE ? 'unlock' : 'lock';

        return response()->json([
            'success' => true,
            'status' => $newStatus,
            'statusText' => $statusText,
            'statusClass' => $statusClass,
            'statusIcon' => $statusIcon
        ]);
    }

    public function extendTimeExpired($id)
    {
        $this->authorize('edit-account', Account::class);
        $account = Account::query()->find($id);

        if(!$account){
            $this->flashMessage('warning', 'Account not found!', 'danger');
            return redirect()->route('account');
        }

        // @noinspection PhpUndefinedFieldInspection
        $timeExpired = $account->time_expired;
        $timeExpired = DateTime::createFromFormat("Y-m-d H:i:s", $timeExpired);
        return view('account.extend_time_expired', compact('account', 'timeExpired'));
    }

    public function updateTimeExpired(UpdateTimeExpiredRequest $request, $id): RedirectResponse
    {
        $this->authorize('edit-account', Account::class);
        $account = Account::query()->find($id);

        if($account == null){
            $this->flashMessage('warning', 'Account not found!', 'danger');
            // @noinspection PhpUndefinedFieldInspection
            return redirect()->route('account.extend_time_expired', ['id' => $account->id]);
        }

        $timeExpired = DateTime::createFromFormat("Y-m-d", $request->input('time_expired'));
        // @noinspection PhpUndefinedFieldInspection
        $account->time_expired = $timeExpired;
        $account->save();

        // @noinspection PhpUndefinedFieldInspection
        return redirect()->route('account.show', ['id' => $account->id]);
    }

    public function destroy($id): RedirectResponse
    {
        $this->authorize('destroy-account', Account::class);
        $account = Account::query()->find($id);

        if(!$account){
            $this->flashMessage('warning', 'Account not found!', 'danger');
            return redirect()->route('account');
        }

        $account->delete();
        $this->flashMessage('check', 'Account successfully deleted!', 'success');

        return redirect()->route('account');
    }

    public function bulkDestroy(IdsStringRequest $request)
    {
        $this->authorize('destroy-account', Account::class);
        $idsString = $request->input('ids');

        if($idsString != null && $idsString != ""){
            $ids = explode(',', $idsString);
        } else {
            $this->flashMessage('warning', __('messages.alert.add.not_found'), 'danger');
            return redirect()->route('account');

        }

        DB::beginTransaction();
        $accounts = Account::query()->whereIn('id', $ids)->get();

        foreach($accounts as $account){
            $account->delete();
        }

        DB::commit();
        $this->flashMessage('check', __('messages.alert.delete.successfully'), 'success');
        return redirect()->route('account');
    }

    public function bulkLocked(IdsStringRequest $request)
    {
        $this->authorize('edit-account', Account::class);
        $idsString = $request->input('ids');

        if($idsString != null && $idsString != ""){
            $ids = explode(',', $idsString);
        } else {
            $this->flashMessage('warning', __('messages.alert.add.not_found'), 'danger');
            return redirect()->route('account');

        }

        DB::beginTransaction();
        $accounts = Account::query()->whereIn('id', $ids)->get();

        foreach($accounts as $account){
            $account->fill([
                'status' => Account::STATUS_LOCK
            ]);
            $account->save();
        }

        DB::commit();
        $this->flashMessage('check', __('messages.alert.locked.successfully'), 'success');
        return redirect()->route('account');
    }

    public function bulkExtendExpired(IdsStringRequest $request)
    {
        $this->authorize('edit-account', Account::class);
        $idsString = $request->input('ids');
        $timeExpired = DateTime::createFromFormat("Y-m-d", $request->input('time_expired'));

        if($idsString != null && $idsString != ""){
            $ids = explode(',', $idsString);
        } else {
            $this->flashMessage('warning', __('messages.alert.add.not_found'), 'danger');
            return redirect()->route('account');
        }

        DB::beginTransaction();
        $accounts = Account::query()->whereIn('id', $ids)->get();

        foreach($accounts as $account){
            $account->fill([
                'time_expired' => $timeExpired
            ]);
            $account->save();
        }

        DB::commit();
        $this->flashMessage('check', __('messages.alert.extend_expired.successfully'), 'success');
        return redirect()->route('account');
    }

    public function bulkUpdate(IdsStringRequest $request)
    {
        $this->authorize('edit-account', Account::class);
        $idsString = $request->input('ids');

        if($idsString != null && $idsString != ""){
            $ids = explode(',', $idsString);
        } else {
            $this->flashMessage('warning', __('messages.alert.add.not_found'), 'danger');
            return redirect()->route('account');
        }

        DB::beginTransaction();
        $accounts = Account::query()->whereIn('id', $ids)->get();

        // Prepare update data
        $updateData = [];

        // Check if time_expired is provided
        if ($request->has('time_expired') && $request->input('time_expired') != null) {
            $timeExpired = DateTime::createFromFormat("Y-m-d", $request->input('time_expired'));
            $updateData['time_expired'] = $timeExpired;
        }

        // Check if province is provided
        if ($request->has('province') && $request->input('province') != -1) {
            $updateData['province'] = $request->input('province');
        }

        // Check if district is provided
        if ($request->has('district') && $request->input('district') != -1) {
            $updateData['district'] = $request->input('district');
        }

        // Check if status is provided
        if ($request->has('status') && $request->input('status') != null) {
            $updateData['status'] = $request->input('status');
        }

        // Update accounts if there's data to update
        if (!empty($updateData)) {
            foreach($accounts as $account){
                $account->fill($updateData);
                $account->save();
            }
        }

        DB::commit();
        $this->flashMessage('check', __('messages.alert.edit.successfully'), 'success');
        return redirect()->route('account');
    }

    public function export()
    {
        return Excel::download(new AccountExport(), 'account.xlsx', \Maatwebsite\Excel\Excel::XLSX);
    }

    public function exportTemplate()
    {
        return Excel::download(new AccountTemplateExport(), 'account_template.xlsx', \Maatwebsite\Excel\Excel::XLSX);
    }

    public function exportSchools()
    {
        return \Maatwebsite\Excel\Facades\Excel::download(new \App\Exports\SchoolExport(), 'schools.xlsx', \Maatwebsite\Excel\Excel::XLSX);
    }

    public function import(Request $request, \Maatwebsite\Excel\Excel $excel)
    {
        ini_set('max_execution_time', 0);
        $path = $request->file('file');
        $import = new AccountImport();
        $excel->import($import, $path);
        $failures = $import->getLogicErrors();

        if ($failures != null) {
            $this->flashMessage('warning', $failures, 'danger');
            return redirect()->route('account');
        }

        if ($import->importedCount() === 0) {
            $this->flashMessage('warning', __('messages.alert.add.empty_records'), 'danger');
            return redirect()->route('account');
        }

        $this->flashMessage('check', __('messages.alert.add.import_done', ['count' => $import->importedCount()]), 'success');
        return redirect()->route('account');
    }

    /**
     * Normalize search keyword by removing Vietnamese diacritics for flexible search
     */
    private function normalizeSearchKeyword($keyword)
    {
        $search = [
            'à', 'á', 'ạ', 'ả', 'ã', 'â', 'ầ', 'ấ', 'ậ', 'ẩ', 'ẫ', 'ă', 'ằ', 'ắ', 'ặ', 'ẳ', 'ẵ',
            'è', 'é', 'ẹ', 'ẻ', 'ẽ', 'ê', 'ề', 'ế', 'ệ', 'ể', 'ễ',
            'ì', 'í', 'ị', 'ỉ', 'ĩ',
            'ò', 'ó', 'ọ', 'ỏ', 'õ', 'ô', 'ồ', 'ố', 'ộ', 'ổ', 'ỗ', 'ơ', 'ờ', 'ớ', 'ợ', 'ở', 'ỡ',
            'ù', 'ú', 'ụ', 'ủ', 'ũ', 'ư', 'ừ', 'ứ', 'ự', 'ử', 'ữ',
            'ỳ', 'ý', 'ỵ', 'ỷ', 'ỹ',
            'đ',
            'À', 'Á', 'Ạ', 'Ả', 'Ã', 'Â', 'Ầ', 'Ấ', 'Ậ', 'Ẩ', 'Ẫ', 'Ă', 'Ằ', 'Ắ', 'Ặ', 'Ẳ', 'Ẵ',
            'È', 'É', 'Ẹ', 'Ẻ', 'Ẽ', 'Ê', 'Ề', 'Ế', 'Ệ', 'Ể', 'Ễ',
            'Ì', 'Í', 'Ị', 'Ỉ', 'Ĩ',
            'Ò', 'Ó', 'Ọ', 'Ỏ', 'Õ', 'Ô', 'Ồ', 'Ố', 'Ộ', 'Ổ', 'Ỗ', 'Ơ', 'Ờ', 'Ớ', 'Ợ', 'Ở', 'Ỡ',
            'Ù', 'Ú', 'Ụ', 'Ủ', 'Ũ', 'Ư', 'Ừ', 'Ứ', 'Ự', 'Ử', 'Ữ',
            'Ỳ', 'Ý', 'Ỵ', 'Ỷ', 'Ỹ',
            'Đ'
        ];

        $replace = [
            'a', 'a', 'a', 'a', 'a', 'a', 'a', 'a', 'a', 'a', 'a', 'a', 'a', 'a', 'a', 'a', 'a',
            'e', 'e', 'e', 'e', 'e', 'e', 'e', 'e', 'e', 'e', 'e',
            'i', 'i', 'i', 'i', 'i',
            'o', 'o', 'o', 'o', 'o', 'o', 'o', 'o', 'o', 'o', 'o', 'o', 'o', 'o', 'o', 'o', 'o',
            'u', 'u', 'u', 'u', 'u', 'u', 'u', 'u', 'u', 'u', 'u',
            'y', 'y', 'y', 'y', 'y',
            'd',
            'a', 'a', 'a', 'a', 'a', 'a', 'a', 'a', 'a', 'a', 'a', 'a', 'a', 'a', 'a', 'a', 'a',
            'e', 'e', 'e', 'e', 'e', 'e', 'e', 'e', 'e', 'e', 'e',
            'i', 'i', 'i', 'i', 'i',
            'o', 'o', 'o', 'o', 'o', 'o', 'o', 'o', 'o', 'o', 'o', 'o', 'o', 'o', 'o', 'o', 'o',
            'u', 'u', 'u', 'u', 'u', 'u', 'u', 'u', 'u', 'u', 'u',
            'y', 'y', 'y', 'y', 'y',
            'd'
        ];

        return strtolower(str_replace($search, $replace, $keyword));
    }

    /**
     * Build a SQL query that normalizes Vietnamese characters in database field for searching
     */
    private function buildNormalizedSearchQuery($fieldName)
    {
        $replacements = [
            'à' => 'a', 'á' => 'a', 'ạ' => 'a', 'ả' => 'a', 'ã' => 'a', 'â' => 'a', 'ầ' => 'a', 'ấ' => 'a', 'ậ' => 'a', 'ẩ' => 'a', 'ẫ' => 'a', 'ă' => 'a', 'ằ' => 'a', 'ắ' => 'a', 'ặ' => 'a', 'ẳ' => 'a', 'ẵ' => 'a',
            'è' => 'e', 'é' => 'e', 'ẹ' => 'e', 'ẻ' => 'e', 'ẽ' => 'e', 'ê' => 'e', 'ề' => 'e', 'ế' => 'e', 'ệ' => 'e', 'ể' => 'e', 'ễ' => 'e',
            'ì' => 'i', 'í' => 'i', 'ị' => 'i', 'ỉ' => 'i', 'ĩ' => 'i',
            'ò' => 'o', 'ó' => 'o', 'ọ' => 'o', 'ỏ' => 'o', 'õ' => 'o', 'ô' => 'o', 'ồ' => 'o', 'ố' => 'o', 'ộ' => 'o', 'ổ' => 'o', 'ỗ' => 'o', 'ơ' => 'o', 'ờ' => 'o', 'ớ' => 'o', 'ợ' => 'o', 'ở' => 'o', 'ỡ' => 'o',
            'ù' => 'u', 'ú' => 'u', 'ụ' => 'u', 'ủ' => 'u', 'ũ' => 'u', 'ư' => 'u', 'ừ' => 'u', 'ứ' => 'u', 'ự' => 'u', 'ử' => 'u', 'ữ' => 'u',
            'ỳ' => 'y', 'ý' => 'y', 'ỵ' => 'y', 'ỷ' => 'y', 'ỹ' => 'y',
            'đ' => 'd',
            'À' => 'a', 'Á' => 'a', 'Ạ' => 'a', 'Ả' => 'a', 'Ã' => 'a', 'Â' => 'a', 'Ầ' => 'a', 'Ấ' => 'a', 'Ậ' => 'a', 'Ẩ' => 'a', 'Ẫ' => 'a', 'Ă' => 'a', 'Ằ' => 'a', 'Ắ' => 'a', 'Ặ' => 'a', 'Ẳ' => 'a', 'Ẵ' => 'a',
            'È' => 'e', 'É' => 'e', 'Ẹ' => 'e', 'Ẻ' => 'e', 'Ẽ' => 'e', 'Ê' => 'e', 'Ề' => 'e', 'Ế' => 'e', 'Ệ' => 'e', 'Ể' => 'e', 'Ễ' => 'e',
            'Ì' => 'i', 'Í' => 'i', 'Ị' => 'i', 'Ỉ' => 'i', 'Ĩ' => 'i',
            'Ò' => 'o', 'Ó' => 'o', 'Ọ' => 'o', 'Ỏ' => 'o', 'Õ' => 'o', 'Ô' => 'o', 'Ồ' => 'o', 'Ố' => 'o', 'Ộ' => 'o', 'Ổ' => 'o', 'Ỗ' => 'o', 'Ơ' => 'o', 'Ờ' => 'o', 'Ớ' => 'o', 'Ợ' => 'o', 'Ở' => 'o', 'Ỡ' => 'o',
            'Ù' => 'u', 'Ú' => 'u', 'Ụ' => 'u', 'Ủ' => 'u', 'Ũ' => 'u', 'Ư' => 'u', 'Ừ' => 'u', 'Ứ' => 'u', 'Ự' => 'u', 'Ử' => 'u', 'Ữ' => 'u',
            'Ỳ' => 'y', 'Ý' => 'y', 'Ỵ' => 'y', 'Ỷ' => 'y', 'Ỹ' => 'y',
            'Đ' => 'd'
        ];

        $query = "LOWER($fieldName)";

        foreach ($replacements as $char => $replacement) {
            $query = "REPLACE($query, '$char', '$replacement')";
        }

        return "$query LIKE ?";
    }
}
