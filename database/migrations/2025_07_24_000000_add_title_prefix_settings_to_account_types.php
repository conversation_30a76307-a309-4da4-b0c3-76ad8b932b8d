<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddTitlePrefixSettingsToAccountTypes extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('account_types', function (Blueprint $table) {
            $table->boolean('enable_title_prefix')->default(false)->after('last_materials_update');
            $table->string('title_prefix_format')->default('Bài {number} - ')->after('enable_title_prefix');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('account_types', function (Blueprint $table) {
            $table->dropColumn('enable_title_prefix');
            $table->dropColumn('title_prefix_format');
        });
    }
}
